import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
from requests.adapters import HTTPAdapter
from urllib3.util import Retry
import datetime
import opencc  # 导入opencc
from openai import OpenAI
from docx.shared import Pt
from docx.oxml.ns import qn  # 导入用于设置中文字体的命名空间
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from collections import defaultdict
import jieba

# 创建繁简转换器
converter = opencc.OpenCC('t2s')  # 繁体转简体

def create_session():
    """创建一个带有重试机制的session"""
    session = requests.Session()
    retry_strategy = Retry(
        total=5,
        backoff_factor=1,
        status_forcelist=[500, 502, 503, 504, 429]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def get_news_details(url, headers, proxies, session):
    for attempt in range(3):
        try:
            response = session.get(
                url, 
                headers=headers, 
                proxies=proxies, 
                timeout=20,
                verify=False
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 获取新闻发布时间
            article_info = soup.find('div', class_='article_info')
            news_time = None
            if article_info:
                time_div = article_info.find('div', class_='time')
                if time_div and (time_span := time_div.find('span')):
                    news_time = time_span.get_text(strip=True)
            
            # 尝试多个可能的正文容器类名
            content = None
            possible_content_classes = [
                'post-content',
                'article-content',
                'content',
                'artText',
                'article-body'
            ]
            
            for class_name in possible_content_classes:
                content = soup.find('div', class_=class_name)
                if content:
                    break
            
            if content:
                # 移除脚本和样式元素
                for element in content.find_all(['script', 'style']):
                    element.decompose()
                
                # 获取所有段落文本
                paragraphs = content.find_all('p')
                if paragraphs:
                    # 合并所有段落，保持适当的空格
                    text = '\n'.join(p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True))
                else:
                    # 如果没有找到段落标签，直接获取文本
                    text = content.get_text(strip=True)
                
                # 清理文本
                text = ' '.join(text.split())  # 规范化空白字符
                text = converter.convert(text)
                return news_time, text if text else "未找到正文内容"
            
            # 如果上述方法都失败，尝试直接查找所有段落
            paragraphs = soup.find_all('p')
            if paragraphs:
                text = '\n'.join(p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True))
                text = converter.convert(text)
                return news_time, text if text else "未找到正文内容"
            
            return news_time, "未找到正文内容"
            
        except Exception as e:
            print(f"获取新闻详情第 {attempt + 1} 次尝试失败: {str(e)}")
            if attempt < 2:
                time.sleep(random.uniform(3, 5))
            else:
                print(f"无法获取新闻详情: {url}")
                return None, "获取失败"




def process_time(time_str):
    """处理时间字符串，将相对时间转换为具体时间"""
    if not time_str:
        return None
    
    # 如果包含"小时前"或"天前"，返回None
    if "小时前" in time_str or "天前" in time_str:
        return None
        
    try:
        # 处理其他时间格式
        # 如果时间已经是"年月日"格式，直接标准化
        if '年' in time_str and '月' in time_str and '日' in time_str:
            # 确保时间格式完整，如果没有时分，添加"00:00"
            if len(time_str.split()) == 1:  # 只有日期没有时间
                time_str = time_str + " 00:00"
            dt = datetime.datetime.strptime(time_str.strip(), '%Y年%m月%d日 %H:%M')
        else:
            # 尝试转换其他可能的格式
            try:
                dt = datetime.datetime.strptime(time_str.strip(), '%Y-%m-%d %H:%M')
            except:
                return None
        
        # 转换为年月日格式
        return dt.strftime('%Y年%m月%d日 %H:%M')
    except:
        return None

def crawl_news(pages):
    news_data = []
    session = create_session()
    
    proxies = {
        'http': 'http://127.0.0.1:7890',
        'https': 'http://127.0.0.1:7890'
    }
    
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Firefox/89.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ]

    try:
        print("测试代理连接...")
        test_response = session.get(
            'https://api.ipify.org?format=json',
            proxies=proxies,
            timeout=10,
            verify=False
        )
        print(f"代理IP: {test_response.json()['ip']}")
    except Exception as e:
        print(f"代理测试失败: {str(e)}")
        return
    
    for page in range(1, pages + 1):
        try:
            headers = {
                'User-Agent': random.choice(user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            }
            
            # 修改为新的URL
            if page == 1:
                url = 'https://www.ntdtv.com/b5/instant-news.html'
                # 即时新闻 instant-news    https://www.ntdtv.com/b5/instant-news.html?paged=2   top-headline
            else:
                url = f'https://www.ntdtv.com/b5/instant-news.html?paged={page}'
            
            print(f"正在获取第 {page} 页...")
            response = session.get(
                url,
                headers=headers,
                proxies=proxies,
                timeout=20,
                verify=False
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            # 修改为适合新页面的选择器
            # news_items = soup.find_all('div', class_='one_post')
            # 修改为适合新页面的选择器
            news_items = soup.select('div.list_wrapper > div.one_post')


            for item in news_items:
                try:
                    date = item.find('div', class_='date')
                    date = date.get_text(strip=True) if date else ""
                    
                    
                    
                    title_div = item.find('div', class_='title')
                    if title_div and (title_tag := title_div.find('a')):
                        title = title_tag.get_text(strip=True)
                        title = converter.convert(title)
                        link = title_tag['href']
                        
                        print(f"正在获取: {title}")
                        news_time, content = get_news_details(link, headers, proxies, session)
                        
                        
                        
                        # 如果新闻详情没有时间，用列表页时间替代
                        news_time = news_time or date
                        
                        print(f"正在获取: {title}")
                        print(f"获取到的内容长度: {len(content) if content else 0}")
                        if not content or len(content) < 50:
                            print(f"警告: 获取到的内容可能不完整: {content[:100]}")
                        
                        news_data.append({
                            '发布时间': news_time,
                            '标题': title,
                            '链接': link,
                            '正文': content
                        })
                        
                        time.sleep(random.uniform(2, 4))
                        
                except Exception as e:
                    print(f"处理单条新闻时出错: {str(e)}")
                    continue
            
            print(f"第 {page} 页爬取完成")
            time.sleep(random.uniform(4, 6))
            
        except Exception as e:
            print(f"爬取第 {page} 页时出错: {str(e)}")
            continue
    
    global csv_filename

    if news_data:
        df = pd.DataFrame(news_data)
        # 再次确保过滤掉所有相对时间的新闻
        df = df[~df['发布时间'].str.contains('小時前', na=False)]
        
        processed_data = []
        for item in news_data:
            processed_time = process_time(item['发布时间'])
            if processed_time:
                item['发布时间'] = processed_time
                processed_data.append(item)
                
        current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            excel_filename = f'ntdtv_news_{current_time}.xlsx'
            df['网站来源'] = '新唐人电视台'
            df.to_excel(excel_filename, index=False)
            print(f"数据已保存到 {excel_filename}")
            csv_filename = excel_filename
        except Exception as e:
            print(f"保存Excel文件时出错: {str(e)}")
            try:
                csv_filename = f'ntdtv_news_{current_time}.csv'
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"数据已保存到 {csv_filename}")
            except Exception as e:
                print(f"保存CSV文件时也出错: {str(e)}")
    else:
        print("没有获取到任何数据")



## 爬取新闻运行

import warnings
warnings.filterwarnings('ignore')
csv_filename = None  # 初始化全局变量
try:
    requests.packages.urllib3.disable_warnings()
    crawl_news(2)  # 爬取1页数据  2页
except KeyboardInterrupt:
    print("\n程序被用户中断")
except Exception as e:
    print(f"程序出现异常: {str(e)}")


# import requests
# from bs4 import BeautifulSoup
# import time
# import json
# from datetime import datetime
# import logging
# import os
# import pandas as pd  # 新增导入

# class VOAScraper:
#     def __init__(self):
#         self.base_url = 'https://www.voachinese.com/z/1739'
#         self.headers = {
#             'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
#             'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
#             'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
#             'Connection': 'keep-alive'
#         }
#         self.proxies = {
#             'http': 'http://127.0.0.1:7890',
#             'https': 'http://127.0.0.1:7890'
#         }
#         self.setup_logging()
        
#     def setup_logging(self):
#         logging.basicConfig(
#             level=logging.INFO,
#             format='%(asctime)s - %(levelname)s - %(message)s',
#             handlers=[
#                 logging.FileHandler('voa_scraper.log', encoding='utf-8'),
#                 logging.StreamHandler()
#             ]
#         )
#         self.logger = logging.getLogger(__name__)

#     def get_page_content(self, url, retries=3):
#         for attempt in range(retries):
#             try:
#                 self.logger.info(f"正在获取页面: {url}")
#                 response = requests.get(
#                     url, 
#                     headers=self.headers, 
#                     proxies=self.proxies,
#                     timeout=30
#                 )
#                 response.raise_for_status()
#                 if response.status_code == 200:
#                     return response.text
#             except requests.RequestException as e:
#                 self.logger.error(f"第 {attempt + 1} 次尝试失败: {str(e)}")
#                 if attempt < retries - 1:
#                     wait_time = 2 ** attempt  # 指数退避
#                     self.logger.info(f"等待 {wait_time} 秒后重试...")
#                     time.sleep(wait_time)
#                 continue
#         self.logger.error(f"在 {retries} 次尝试后获取页面失败")
#         return None

#     def parse_article_list(self, html_content):
#         if not html_content:
#             return []

#         soup = BeautifulSoup(html_content, 'html.parser')
#         articles = []
        
#         # 查找新闻列表
#         news_items = soup.find_all('div', class_='media-block')
        
#         for item in news_items:
#             try:
#                 # 获取标题
#                 title_element = item.find('h4', class_='media-block__title')
#                 if not title_element:
#                     continue
#                 title = title_element.text.strip()
                
#                 # 获取链接
#                 link_element = item.find('a', href=True)
#                 if not link_element:
#                     continue
#                 link = 'https://www.voachinese.com' + link_element['href']
                
#                 # 获取日期
#                 date_element = item.find('span', class_='date')
#                 date = date_element.text.strip() if date_element else ''
                
#                 # 获取摘要
#                 summary_element = item.find('p', class_='perex')
#                 summary = summary_element.text.strip() if summary_element else ''
                
#                 # 获取文章全文
#                 article_content = self.get_article_content(link)
                
#                 article = {
#                     'title': title,
#                     'link': link,
#                     'date': date,
#                     'summary': summary,
#                     'content': article_content
#                 }
#                 articles.append(article)
#                 self.logger.info(f"成功解析文章: {title}")
#                 time.sleep(1)  # 添加延迟，避免请求过快
                
#             except Exception as e:
#                 self.logger.error(f"解析文章时出错: {e}")
#                 continue
                
#         return articles

#     def get_article_content(self, url):
#         """获取文章全文内容"""
#         try:
#             html_content = self.get_page_content(url)
#             if not html_content:
#                 return ""
                
#             soup = BeautifulSoup(html_content, 'html.parser')
#             # 查找文章主体内容
#             content_div = soup.find('div', class_='content-offset')
#             if not content_div:
#                 return ""
            
#             # 获取所有段落
#             paragraphs = content_div.find_all(['p', 'h2', 'h3'])
#             content = '\n'.join([p.text.strip() for p in paragraphs if p.text.strip()])
#             return content
            
#         except Exception as e:
#             self.logger.error(f"获取文章内容时出错: {e}")
#             return ""

#     def save_to_json(self, articles, filename=None):
#         if not articles:
#             self.logger.warning("没有文章需要保存")
#             return

#         try:
#             # 转换VOA数据格式
#             voa_df = pd.DataFrame([{
#                 '发布时间': article['date'],
#                 '标题': article['title'],
#                 '链接': article['link'],
#                 '正文': article['content']
#             } for article in articles])
            
#             # 添加来源列
#             voa_df['网站来源'] = '美国之音中文网'
            
#             # 如果存在新唐人的Excel文件，则读取并合并
#             if csv_filename and os.path.exists(csv_filename):
#                 ntd_df = pd.read_excel(csv_filename)
#                 # 添加来源列
#                 ntd_df['网站来源'] = '新唐人电视台'
                
#                 # 合并两个数据框
#                 combined_df = pd.concat([ntd_df, voa_df], ignore_index=True)
                
#                 # 保存合并后的数据
#                 combined_df.to_excel(csv_filename, index=False)
#                 self.logger.info(f"数据已追加保存到 {csv_filename}")
#             else:
#                 self.logger.warning("未找到新唐人新闻文件，将只保存VOA新闻数据")
#                 current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
#                 new_filename = f'combined_news_{current_time}.xlsx'
#                 voa_df.to_excel(new_filename, index=False)
#                 self.logger.info(f"数据已保存到新文件 {new_filename}")

#         except Exception as e:
#             self.logger.error(f"保存数据时出错: {e}")
#             raise

#     def run(self):
#         self.logger.info("开始爬取 VOA 中文网...")
#         html_content = self.get_page_content(self.base_url)
#         if html_content:
#             articles = self.parse_article_list(html_content)
#             self.logger.info(f"共爬取到 {len(articles)} 篇文章")
#             self.save_to_json(articles)
#         else:
#             self.logger.error("爬取失败")

# if __name__ == "__main__":
#     scraper = VOAScraper()
#     scraper.run()



from openai import OpenAI
import pandas as pd
from docx import Document
import datetime
import json
import os
import requests
import urllib3
import time
from docx.shared import Pt
# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class NewsProcessor:
    def __init__(self, config_path='config.json'):
        """
        初始化新闻处理器
        config_path: 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.client = self.setup_openai()  # 修改为创建client

    def load_config(self, config_path):
        """加载配置文件"""
        try:
            if not os.path.exists(config_path):
                default_config = {
                    "openai": {
                        "api_key": "your-api-key-here",
                        "api_base": "https://api.openai.com/v1",
                        "model": "gpt-3.5-turbo"
                    },
                    "proxy": {
                        "http": "http://127.0.0.1:7890",
                        "https": "http://127.0.0.1:7890"
                    },
                    "news": {
                        "top_news_count": 10,
                        "min_score": 50
                    }
                }
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4)
                print(f"已创建默认配置文件: {config_path}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"加载配置文件时出错: {str(e)}")
            raise

    def setup_openai(self):
        """设置OpenAI配置"""
        try:
            client = OpenAI(
                api_key=self.config['openai']['api_key'],
                base_url=self.config['openai']['api_base']
            )
            self.model = self.config['openai']['model']
            return client
        except Exception as e:
            print(f"设置OpenAI配置时出错: {str(e)}")
            raise
    def create_session(self):
        """创建一个带有代理设置的session"""
        session = requests.Session()
        if 'proxy' in self.config:
            session.proxies.update(self.config['proxy'])
        session.verify = False  # 禁用SSL验证
        return session

    def analyze_importance(self, title):
        """使用OpenAI分析新闻标题的重要性"""
        try:
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": """
                            作为中国政府的情报分析专家,请对此新闻标题进行严格的战略价值评估。评估维度如下:

                            1. 国家安全影响度 (权重40%)
                               - 军事、外交、国防、科技等核心领域
                               - 主权、领土、统一等根本利益
                               - 关键基础设施与战略资源
                               - 网络空间安全
                            
                            2. 经济战略价值 (权重30%) 
                               - 关键核心技术与产业链安全
                               - 金融、贸易、投资风险
                               - 能源资源战略布局
                               - 国际经济博弈态势
                            
                            3. 政治社会影响 (权重20%)
                               - 政治安全与意识形态
                               - 社会稳定与民生
                               - 舆论导向与社会心理
                               - 境外敌对势力渗透
                            
                            4. 时效紧迫度 (权重10%)
                               - 情报时效性与预警价值
                               - 危机发展趋势研判
                               - 应对窗口期判断

                            请给出1-100的综合评分,并简要说明理由。

                            低价值信息(评分上限10分):
                            - 生活建议，出行建议
                            - 境外社会民生新闻
                            - 官员个人事务(贪腐/人事/财产等)
                            - 一般事故灾害
                            - 非战略性医疗卫生
                            - 宗教文化活动
                            - 普通商业信息
                            - 娱乐体育资讯
                            - 生活服务类信息
                            - 器官相关内容
                            - 活摘相关内容
                            - 官员权力斗争政治格局变换等
                            - 官员个人生活
                            - 宗教
                            - 中国共产党负面新闻
                            - 中国官员斗争
                            - 中国官员贪腐
                            - 中国官员财产
                            - 中国官员人事
                            - 中国官员个人事务
                            - 中国太子党，中央某官员下台
                            
                            
                            高价值信息(基准分60分起):
                            - 重大军事部署与武器装备
                            - 关键技术突破与产业升级
                            - 重要外交动态与国际局势
                            - 能源资源与战略物资
                            - 网络空间主权与安全
                            - 涉及主权领土完整
                            - 国际贸易摩擦与制裁
                            - 敌对势力渗透与干预
                            - 重大区域安全态势
                            """},
                            {"role": "user", "content": title}
                        ]
                    )
                    return response.choices[0].message.content
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    print(f"第 {attempt + 1} 次尝试失败，正在重试...")
                    time.sleep(2)
        except Exception as e:
            print(f"分析标题时出错: {str(e)}")
            return "分析失败"

    def extract_score(self, analysis):
        """从分析结果中提取分数"""
        try:
            import re
            numbers = re.findall(r'\d+', analysis)
            if numbers:
                for num in numbers:
                    score = int(num)
                    if 1 <= score <= 100:
                        return score
            return 0
        except:
            return 0


    def analyze_content(self, title, content, source):
        """分析新闻内容（同步版本）"""
        try:
            # 确保content是字符串类型
            if pd.isna(content) or not content:
                content = ""
            else:
                content = str(content)
            
            # 清理内容中的多余换行和空格
            cleaned_content = ' '.join(content.replace('\n', ' ').split())
            
            prompt = f"""
            请根据以下新闻进行分析，要求:
            1. 直接以新闻媒体名称和"发文称:"开头，使用提供的来源: "{source}"
            2. 用简洁的新闻写作风格
            3. 直接引述新闻的核心内容
            4. 突出地点、人物等关键信息
            5. 整体篇幅控制在200字左右
            6. 使用客观陈述的语气
            
            新闻标题：{title}
            新闻正文：{cleaned_content}
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的新闻编辑,专注于新闻要点提炼和核心信息陈述。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500,
                presence_penalty=-0.1,
                frequency_penalty=0.1
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"分析内容时出错: {str(e)}")
            return "内容分析失败"
        
    def format_time(self, news_time):
        """
        简化格式化时间为"某年某月某日"的格式
        所有无效时间都返回当天日期，避免NaT错误
        """
        today = datetime.datetime.now().strftime('%Y年%m月%d日')
        
        # 提前处理所有可能导致NaT的情况
        if news_time is None or pd.isna(news_time):
            return today
        
        try:
            # 如果已经是datetime类型，直接使用
            if isinstance(news_time, datetime.datetime):
                return news_time.strftime('%Y年%m月%d日')
            
            # 如果是Timestamp类型，转换为datetime
            if isinstance(news_time, pd.Timestamp):
                return news_time.to_pydatetime().strftime('%Y年%m月%d日')
            
            # 如果是字符串，尝试转换
            if isinstance(news_time, str):
                try:
                    dt = pd.to_datetime(news_time)
                    if pd.isna(dt):  # 再次检查转换结果
                        return today
                    return dt.strftime('%Y年%m月%d日')
                except:
                    return today
                
            # 其他所有情况返回当天日期
            return today
        
        except Exception as e:
            print(f"时间格式化错误: {str(e)}")
            return today
    


    def save_high_score_news_to_word(self, top_news, original_df, export_count=6):
        """保存评分最高的新闻到Word文档"""
        doc = Document()
        
        # 设置默认段落格式
        style = doc.styles['Normal']
        font = style.font
        # 设置英文字体为Times New Roman
        font.name = 'Times New Roman'
        font.size = Pt(16)
        # 设置中文字体为仿宋GB2312
        font._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        # 设置西文字体为Times New Roman
        font._element.rPr.rFonts.set(qn('w:ascii'), 'Times New Roman')
        font._element.rPr.rFonts.set(qn('w:hAnsi'), 'Times New Roman')
        
        # 设置标题样式
        heading_style = doc.styles['Normal']  # 使用Normal样式作为标题样式
        heading_font = heading_style.font
        heading_font.name = 'Times New Roman'
        heading_font.size = Pt(16)
        heading_font._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        heading_font._element.rPr.rFonts.set(qn('w:ascii'), 'Times New Roman')
        heading_font._element.rPr.rFonts.set(qn('w:hAnsi'), 'Times New Roman')
        
        # doc.add_heading(f'评分最高的前 {export_count} 条新闻', level=1)

        for i, news in enumerate(top_news[:export_count], 1):
            try:
                original_news = original_df[original_df['标题'] == news['title']].iloc[0]
                
                # 确保正文是字符串类型
                content = str(original_news['正文']) if not pd.isna(original_news['正文']) else ""
                # 清洗正文末尾的"评论区"字样
                content = content.rstrip("评论区")
                cleaned_content = ' '.join(content.split())
                
                # 格式化时间
                news_time = self.format_time(original_news['发布时间'])
                news_source = str(original_news['网站来源'])
                
                # 添加标题(使用Normal样式)
                title_paragraph = doc.add_paragraph()
                title_paragraph.style = heading_style
                title_paragraph.add_run(f"{i}. {news['title']}")
                
                # 添加来源和时间信息
                source_paragraph = doc.add_paragraph()
                source_paragraph.style = style
                if news_source != "新唐人电视台":
                    source_paragraph.add_run(f"【{news_source}北京时间{news_time}讯】")
                else:
                    print("Not need to add source")
                # 添加正文(带首行缩进)
                paragraph = doc.add_paragraph()
                paragraph.style = style
                paragraph.paragraph_format.first_line_indent = Pt(32)  # 设置首行缩进两个字符(16pt * 2)
                paragraph.paragraph_format.space_after = Pt(0)  # 设置段后间距为0
                paragraph.paragraph_format.space_before = Pt(0)  # 设置段前间距为0
                paragraph.add_run(f"{cleaned_content}")
                
            except Exception as e:
                print(f"处理第 {i} 条高分新闻时出错: {str(e)}")
                continue

        current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        high_score_filename = f'high_score_news_{current_time}.docx'
        doc.save(high_score_filename)

        print(f"评分最高的 {export_count} 条新闻已保存到: {high_score_filename}")

    def preprocess_text(self, text):
        """对文本进行预处理"""
        # 确保文本是字符串类型
        text = str(text) if text is not None else ""
        # 分词
        words = jieba.cut(text)
        # 转换为空格分隔的字符串
        return " ".join(words)

    def calculate_initial_similarity(self, texts):
        """使用TF-IDF和余弦相似度计算初步的相似度"""
        # 创建TF-IDF向量化器
        vectorizer = TfidfVectorizer(
            min_df=2,
            max_df=0.95,
            token_pattern=r'(?u)\b\w+\b'
        )
        
        try:
            # 转换文本为TF-IDF矩阵
            tfidf_matrix = vectorizer.fit_transform(texts)
            # 计算余弦相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)
            return similarity_matrix
        except Exception as e:
            print(f"计算TF-IDF相似度时出错: {str(e)}")
            return np.zeros((len(texts), len(texts)))

    def group_by_time_window(self, news_data, window_hours=48):
        """按时间窗口对新闻进行分组"""
        time_groups = defaultdict(list)
        
        for idx, news in enumerate(news_data):
            try:
                news_time = pd.to_datetime(news['发布时间'])
                # 使用时间戳的日期部分作为分组键
                time_key = news_time.floor('D')
                # 将新闻添加到对应的时间组
                time_groups[time_key].append((idx, news))
            except Exception as e:
                print(f"处理新闻时间分组时出错: {str(e)}")
                continue
        
        # 合并相邻时间窗口的新闻
        merged_groups = []
        sorted_times = sorted(time_groups.keys())
        
        for i, time_key in enumerate(sorted_times):
            current_group = time_groups[time_key]
            # 查找时间窗口内的其他组
            for j in range(i + 1, len(sorted_times)):
                next_time = sorted_times[j]
                if (next_time - time_key).total_seconds() / 3600 <= window_hours:
                    current_group.extend(time_groups[next_time])
            merged_groups.append(current_group)
        
        return merged_groups

    def batch_check_similarity(self, news_list):
        """优化后的新闻相似度检查"""
        try:
            # 预处理新闻数据
            print("正在预处理新闻数据进行相似度分析...")  # 添加明确的阶段标记
            news_data = []
            for idx, news in enumerate(news_list):
                processed_title = self.preprocess_text(news['标题'])
                processed_content = self.preprocess_text(news['正文'])
                news_data.append({
                    'id': idx,
                    '标题': news['标题'],
                    '处理后标题': processed_title,
                    '处理后正文': processed_content,
                    '发布时间': pd.to_datetime(str(news['发布时间'])),
                    'original': news
                })

            # 按时间窗口分组
            print("正在对新闻进行时间分组...")  # 添加明确的阶段标记
            time_groups = self.group_by_time_window(news_data)
            
            removed_ids = set()
            duplicate_pairs = []
            
            # 处理每个时间组
            total_groups = len(time_groups)
            for group_idx, group in enumerate(time_groups):
                if len(group) < 2:
                    continue
                
                # 添加明确的进度信息
                print(f"正在分析时间组 ({group_idx+1}/{total_groups})，包含 {len(group)} 条新闻...")
                    
                group_texts = [news[1]['处理后标题'] + " " + news[1]['处理后正文'] for news in group]
                group_ids = [news[0] for news in group]
                
                # 计算初步相似度
                print(f"正在计算组内新闻相似度...")  # 添加明确的阶段标记
                similarity_matrix = self.calculate_initial_similarity(group_texts)
                
                # 找出相似度高的候选对
                candidates = []
                for i in range(len(group)):
                    for j in range(i + 1, len(group)):
                        if similarity_matrix[i][j] > 0.5:  # 相似度阈值
                            candidates.append((group_ids[i], group_ids[j], similarity_matrix[i][j]))
                
                # 对候选对进行详细检查
                if candidates:
                    print(f"发现 {len(candidates)} 对可能相似的新闻，进行详细分析...")  # 添加明确的阶段标记
                    
                    # 每次处理10个候选对（减少批量大小以提高稳定性）
                    batch_size = 30
                    for i in range(0, len(candidates), batch_size):
                        batch_candidates = candidates[i:i + batch_size]
                        
                        # 添加明确的进度信息
                        print(f"正在分析第 {i//batch_size + 1}/{(len(candidates)-1)//batch_size + 1} 批相似新闻...")
                        
                        # 构建更简洁的prompt
                        prompt = """请分析以下新闻对的相似度。
                        
返回格式要求：
{
    "similar_pairs": [
        {
            "id1": "第一篇新闻ID",
            "id2": "第二篇新闻ID",
            "similarity_type": "title或content或both或different",
            "keep_id": "应保留的新闻ID"
        }
    ]
}

判断标准：
1. 标题相似：完全相同或一个包含另一个的核心内容
2. 内容相似：报道同一事件且核心信息一致
3. 如不相似，标记为different

新闻对比：
"""
                        
                        # 添加候选对信息
                        for idx, (id1, id2, sim_score) in enumerate(batch_candidates, 1):
                            news1 = news_data[id1]
                            news2 = news_data[id2]
                            prompt += f"\n对比组{idx}:\n"
                            prompt += f"新闻1(ID:{id1}): {news1['标题']}\n"
                            prompt += f"新闻2(ID:{id2}): {news2['标题']}\n"
                        
                        try:
                            # 添加重试机制
                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    response = self.client.chat.completions.create(
                                        model=self.model,
                                        messages=[
                                            {"role": "system", "content": "你是一个专业的新闻分析助手，专注于新闻去重。请始终返回有效的JSON格式数据。"},
                                            {"role": "user", "content": prompt}
                                        ],
                                        temperature=0.1,
                                        response_format={"type": "json_object"}  # 强制JSON响应
                                    )
                                    
                                    result = json.loads(response.choices[0].message.content)
                                    
                                    # 验证JSON结构
                                    if not isinstance(result, dict) or 'similar_pairs' not in result:
                                        raise ValueError("Invalid JSON structure")
                                    
                                    # 处理相似对
                                    for pair in result['similar_pairs']:
                                        if pair['similarity_type'] != 'different':
                                            id1, id2 = int(pair['id1']), int(pair['id2'])
                                            if id1 not in removed_ids and id2 not in removed_ids:
                                                keep_id = int(pair['keep_id'])
                                                remove_id = id2 if keep_id == id1 else id1
                                                removed_ids.add(remove_id)
                                                duplicate_pairs.append(pair)
                                    
                                    break  # 成功处理，跳出重试循环
                                    
                                except json.JSONDecodeError as e:
                                    print(f"JSON解析错误，尝试次数 {attempt + 1}/{max_retries}: {e}")
                                    if attempt == max_retries - 1:
                                        print("达到最大重试次数，跳过此批次")
                                except Exception as e:
                                    print(f"处理错误，尝试次数 {attempt + 1}/{max_retries}: {e}")
                                    if attempt == max_retries - 1:
                                        print("达到最大重试次数，跳过此批次")
                                
                            time.sleep(2)  # 重试前等待
                            
                        except Exception as e:
                            print(f"处理批次时出错: {e}")
                            continue
                        
                        time.sleep(1)  # 批次间延迟
            
            # 获取未被移除的新闻
            print("完成相似度分析，正在整理去重结果...")  # 添加明确的阶段标记
            unique_news = [news_list[i] for i in range(len(news_list)) if i not in removed_ids]
            
            print(f"\n去重结果统计：")
            print(f"原始新闻数量: {len(news_list)}")
            print(f"去重后数量: {len(unique_news)}")
            print(f"移除重复数量: {len(removed_ids)}")
            
            return unique_news
            
        except Exception as e:
            print(f"新闻相似度检查过程出错: {e}")
            return news_list

    def filter_sensitive_news(self, news_list):
        """过滤包含敏感关键词的新闻"""
        filtered_news = []
        filtered_count = 0
        sensitive_keywords = ['神韵', '法轮功', '法轮大法', '真善忍', '大法弟子', '李洪志']
        
        for news in news_list:
            # 检查标题
            title = str(news['标题']) if news['标题'] is not None else ""
            
            # 检查正文前100个字符
            content = str(news['正文']) if news['正文'] is not None else ""
            content_preview = content[:100]
            
            # 检查是否包含敏感关键词
            contains_sensitive = False
            for keyword in sensitive_keywords:
                if keyword in title or keyword in content_preview:
                    contains_sensitive = True
                    filtered_count += 1
                    break
            
            if not contains_sensitive:
                filtered_news.append(news)
        
        print(f"过滤掉含敏感关键词的新闻: {filtered_count} 条")
        return filtered_news

    def process_news(self, excel_file, export_count=6):
        """处理新闻主函数"""
        try:
            # 读取Excel文件时就处理好数据类型
            print("正在加载新闻数据...")  # 添加明确的阶段标记
            df = pd.read_excel(excel_file)
            
            # 预处理数据，确保类型正确
            df = df.fillna({
                '发布时间': pd.NaT,
                '正文': '',
                '标题': '',
                '网站来源': ''
            })
            
            # 确保正文是字符串类型
            df['正文'] = df['正文'].astype(str).replace('nan', '')
            
            # 转换时间列
            df['发布时间'] = pd.to_datetime(df['发布时间'], errors='coerce')
            
            # 过滤敏感新闻
            print("开始过滤敏感关键词新闻...")
            news_list = df.to_dict('records')
            filtered_news = self.filter_sensitive_news(news_list)
            df = pd.DataFrame(filtered_news)
            
            print("开始检测并去除相似新闻...")  # 保留原有标记
            unique_news = self.batch_check_similarity(filtered_news)
            unique_df = pd.DataFrame(unique_news)
            
            print(f"去重前新闻数量: {len(df)}")
            print(f"去重后新闻数量: {len(unique_df)}")
            
            # 分析剩余新闻的重要性
            print("开始分析新闻重要性...")  # 保留原有标记
            importance_scores = []
            total_news = len(unique_df)
            
            for idx, row in unique_df.iterrows():
                if pd.isna(row['标题']) or not row['标题'].strip():
                    continue
                
                # 添加进度信息
                print(f"正在评估新闻重要性 ({idx+1}/{total_news}): {row['标题'][:30]}...")
                
                analysis = self.analyze_importance(row['标题'])
                score = self.extract_score(analysis)
                importance_scores.append({
                    'title': row['标题'],
                    'score': score,
                    'analysis': analysis
                })
                print(f"评估完成: {row['标题'][:30]}... 得分: {score}")
            
            # 按重要性排序
            print("正在对新闻进行排序...")  # 添加明确的阶段标记
            sorted_news = sorted(importance_scores, key=lambda x: x['score'], reverse=True)
            top_news = sorted_news[:self.config['news']['top_news_count']]
            
            # 处理并保存结果
            processed_news = []  # 存储处理后的新闻列表
            print("开始分析新闻内容...")  # 修改为更明确的阶段标记
            
            total_top = len(top_news)
            for idx, news in enumerate(top_news):
                try:
                    # 根据标题从unique_df中获取原始新闻数据
                    original_news = unique_df[unique_df['标题'] == news['title']].iloc[0]
                    
                    # 如果正文为空则跳过该条新闻
                    if pd.isna(original_news['正文']) or not str(original_news['正文']).strip():
                        continue
                    
                    # 添加明确的进度信息
                    print(f"正在分析高价值新闻 ({idx+1}/{total_top}): {original_news['标题'][:30]}...")
                    
                    # 调用analyze_content分析新闻内容
                    content_analysis = self.analyze_content(
                        original_news['标题'], 
                        str(original_news['正文']),
                        str(original_news.get('网站来源', '来源未知'))  # 获取新闻来源,默认为"来源未知"
                    )
                    print(f"新闻内容分析完成: {original_news['标题'][:30]}")
                    
                    # 格式化发布时间
                    formatted_time = self.format_time(original_news['发布时间'])
                    
                    # 组装最终的新闻文本,格式为: 时间,来源:标题。内容分析
                    formatted_news = f"{formatted_time}，{original_news.get('网站来源', '来源未知')}：{original_news['标题']}。{content_analysis}"
                    processed_news.append(formatted_news)  # 添加到结果列表
                except Exception as e:
                    print(f"处理单条新闻时出错: {str(e)}")
                    continue  # 出错时跳过当前新闻
            
            # 保存结果
            print("正在生成最终文档...")  # 添加明确的阶段标记
            self.save_to_word(processed_news)
            self.save_high_score_news_to_word(top_news, unique_df, export_count)
            print("文档生成完成，处理结束")  # 添加明确的结束标记
            
        except Exception as e:
            print(f"处理新闻时出错: {str(e)}")

    def save_to_word(self, processed_news):
        """保存为Word文档"""
        doc = Document()
        
        # 设置默认段落格式
        style = doc.styles['Normal']
        font = style.font
        # 设置英文字体为Times New Roman
        font.name = 'Times New Roman'
        font.size = Pt(16)  # 三号字约等于16磅
        # 设置中文字体为仿宋GB2312
        font._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        # 设置西文字体为Times New Roman
        font._element.rPr.rFonts.set(qn('w:ascii'), 'Times New Roman')
        font._element.rPr.rFonts.set(qn('w:hAnsi'), 'Times New Roman')
        
        # 设置段落格式
        paragraph_format = style.paragraph_format
        paragraph_format.line_spacing = Pt(28)  # 固定值28磅
        paragraph_format.first_line_indent = Pt(32)  # 首行缩进两字符
        paragraph_format.space_after = Pt(0)  # 设置段后间距为0
        paragraph_format.space_before = Pt(0)  # 设置段前间距为0
        
        for i, news in enumerate(processed_news, 1):
            # 清理新闻内容中的多余换行和空格
            cleaned_news = ' '.join(news.replace('\n', ' ').split())
            # 替换 ### 为 中共
            cleaned_news = cleaned_news.replace('**###**', '中共')
            # 清除所有星号
            cleaned_news = cleaned_news.replace('*', '')
            cleaned_news = cleaned_news.replace('**', '')
            paragraph = doc.add_paragraph(f"{i}. {cleaned_news}")
            # 确保每个段落都应用相同的格式
            paragraph.style = style
        
        current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_filename = f'processed_news_{current_time}.docx'
        doc.save(doc_filename)
        print(f"处理完成，文件已保存为: {doc_filename}")

    def print_summary(self, top_news):
        """打印分析摘要"""
        print("\n重要性排名前10的新闻：")
        for i, news in enumerate(top_news, 1):
            print(f"\n{i}. {news['title']}")
            print(f"重要性得分: {news['score']}")
            print(f"分析: {news['analysis']}")

def main():
    # 创建配置文件示例
    config_example = {
        "openai": {
            "api_key": "your-api-key-here",
            "api_base": "your-api-base-url-here",
            "model": "gpt-3.5-turbo"
        },
        "proxy": {
            "http": "http://127.0.0.1:7890",
            "https": "http://127.0.0.1:7890"
        },
        "news": {
            "top_news_count": 10,
            "min_score": 10
        }
    }
    
    # 如果没有配置文件，创建示例配置
    if not os.path.exists('config.json'):
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config_example, f, indent=4)
        print("请先在config.json中配置正确的API信息")
        return

    try:
        processor = NewsProcessor()
        # 使用全局变量csv_filename
        if csv_filename:
            processor.process_news(csv_filename)
        else:
            print("没有可用的新闻文件")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()